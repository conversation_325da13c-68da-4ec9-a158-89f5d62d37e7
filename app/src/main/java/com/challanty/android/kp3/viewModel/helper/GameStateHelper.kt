package com.challanty.android.kp3.viewModel.helper

import androidx.compose.ui.unit.IntSize
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.viewModel.TileModel
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject
import kotlin.math.max

/**
 * Helper class for implementing GameViewModel state logic.
 */
@ViewModelScoped
class GameStateHelper @Inject constructor(
    private val gameModelHelper: GameModelHelper
) {

    private val _gameState = MutableStateFlow(GameState())
    val gameState: StateFlow<GameState> = _gameState.asStateFlow()

    fun offsetDuration(): Int {
        // TODO don't hardcode this
        return if (gameModelHelper.animate.value == true &&
            gameModelHelper.animateSwap.value == true) 500 else 0
    }

    fun rotationDuration(): Int {
        // TODO don't hardcode this
        return if (gameModelHelper.animate.value == true &&
            gameModelHelper.animateRotation.value == true) 500 else 0
    }

    fun updateGameState(gameState: GameState) {
        _gameState.value = gameState
    }

    fun toggleProgress(show: Boolean? = null) {
        _gameState.update { currentState ->
            currentState.copy(
                showProgress = show ?: !currentState.showProgress,
                showGameBoard = false,
                showSolvedBoard = false
            )
        }
    }

    fun toggleGameBoard(show: Boolean? = null) {
        _gameState.update { currentState ->
            currentState.copy(
                showGameBoard = show ?: !currentState.showGameBoard,
                showSolvedBoard = false,
                showProgress = false
            )
        }
    }

    fun toggleSolvedBoard(show: Boolean? = null) {
        _gameState.update { currentState ->
            currentState.copy(
                showSolvedBoard = show ?: !currentState.showSolvedBoard,
                showGameBoard = false,
                showProgress = false
            )
        }
    }

    suspend fun handleUITileSwap(
        tile1: TileModel,
        tile2: TileModel
    ) {

        // Save the current offset and board position of tile 1
        val savedOffset = tile1.intOffset
        val savedBoardPosition = tile1.boardPosition

        tile1.update(
            boardPosition = tile2.boardPosition,
            offsetX = tile2.intOffset.x,
            offsetY = tile2.intOffset.y,

            offsetDuration = offsetDuration(),
            quarterTurnCnt = tile1.quarterTurnCnt,
            rotationDuration = rotationDuration()
        )

        tile2.update(
            boardPosition = savedBoardPosition,
            offsetX = savedOffset.x,
            offsetY = savedOffset.y,

            offsetDuration = offsetDuration(),
            quarterTurnCnt = tile2.quarterTurnCnt,
            rotationDuration = rotationDuration()
        )

        // Pause game setup if we are animating so user is blocked from interacting
        delay(max(offsetDuration(), rotationDuration()).toLong())
    }

    suspend fun handleUITileRotation(tile: TileModel) {
        println("handleUITileRotation: Rotating tile ${tile.id}")
        tile.update(
            boardPosition = tile.boardPosition,
            offsetX = tile.intOffset.x,
            offsetY = tile.intOffset.y,
            offsetDuration = offsetDuration(),
            // Don't mod quarterTurnCnt by 4 because the rotation animation will
            // rotate counterclockwise instead of clockwise when the value goes
            // from 270 to 0.
            quarterTurnCnt = tile.quarterTurnCnt + 1,
            rotationDuration = rotationDuration()
        )

        // Pause game setup if we are animating so user is blocked from interacting
        delay(max(offsetDuration(), rotationDuration()).toLong())
    }

    fun initTilePlacement() {
        // Place tiles at offset (0, 0) without rotation or animation
        gameState.value.tileModels.forEach { tileViewModel ->
            tileViewModel.update(
                // Don't change board position
                boardPosition = tileViewModel.boardPosition,
                offsetX = 0,
                offsetY = 0,
                offsetDuration = 0,
                quarterTurnCnt = 0,
                rotationDuration = 0
            )
        }
    }

    suspend fun finalizeTilePlacement() {
        // Move tiles to their game board positions and rotate them with their scramble rotation
        val tilePxSize = gameState.value.tilePxSize

        // First, position all tiles at the upper-left corner (0,0)
        initTilePlacement()

        // Wait a moment for the UI to render with tiles at the upper-left corner
        delay(500) // Short delay to ensure UI is rendered

        // Now move tiles to their final positions and rotate them
        gameState.value.tileModels.forEach { tile ->

            val boardRow = tile.boardPosition.x
            val boardCol = tile.boardPosition.y

            tile.update(
                boardPosition = tile.boardPosition, // Don't change board position
                offsetX = tilePxSize.width * boardCol,
                offsetY = tilePxSize.height * boardRow,
                offsetDuration = offsetDuration(),
                quarterTurnCnt = tile.quarterTurnCnt,
                rotationDuration = rotationDuration()
            )
        }

        // Pause game setup if we are animating so user is blocked from interacting
        delay(max(offsetDuration(), rotationDuration()).toLong())
    }

    suspend fun showStartupGame(gameState: GameState) {
        initTilePlacement()
        delay(100) // Short delay to ensure UI is rendered before we update the state again

        updateGameState(gameState)
        finalizeTilePlacement()
    }

    suspend fun showNewGame(gameState: GameState) {
        // Tiles are already in the correct position and rotation, so we can just update the state
        updateGameState(gameState)
        finalizeTilePlacement()
    }

    suspend fun showExistingGame(
        newGameState: GameState?,
    ) {
        // A new gameState is provided when the game area size changes
        initTilePlacement()
        delay(100) // Short delay to ensure UI is rendered before we update the state again

        val gameState = newGameState ?: gameState.value

        updateGameState(gameState)
        finalizeTilePlacement()
    }
}