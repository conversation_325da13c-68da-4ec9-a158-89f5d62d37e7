package com.challanty.android.kp3.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.challanty.android.kp3.viewModel.TileModel

@Composable
fun GameBoard(
    modifier: Modifier = Modifier,
    boardPxSize: IntSize,
    tilePxSize: IntSize,
    tileModels: List<TileModel>,
    onTileClick: (TileModel) -> Unit,
    onTileDoubleClick: (TileModel) -> Unit,
) {
    println("GameBoard: Composing")
    // Calculate Dp values for us as well as the children since it's a one-time calculation
    // that each child shouldn't have to do.
    val density = LocalDensity.current
    val boardPxSize = boardPxSize
    val tilePxSize = tilePxSize
    val boardWidthDp = with(density) { boardPxSize.width.toDp() }
    val boardHeightDp = with(density) { boardPxSize.height.toDp() }
    val tileWidthDp = with(density) { tilePxSize.width.toDp() }
    val tileHeightDp = with(density) { tilePxSize.height.toDp() }

    Box(
        modifier = modifier
            .size(boardWidthDp, boardHeightDp)
            .border(2.dp, Color.Black)
            .background(Color.Transparent)
            .clipToBounds(), // Ensure tiles don't draw outside if something goes wrong with coords
    ) {
        // Each tile will only recompose when its specific state changes
        tileModels.forEach { tileViewModel ->
//            println("GameBoard: Composing tile ${tileViewModel.id}")
            GameBoardTile(
                tileModel = tileViewModel,
                tileWidthDp = tileWidthDp,
                tileHeightDp = tileHeightDp,
                onTileClick = onTileClick,
                onTileDoubleClick = onTileDoubleClick
            )
        }
    }
}