package com.challanty.android.kp3.di

import com.challanty.android.kp3.data.repository.GameRepository
import com.challanty.android.kp3.data.repository.GameRepositoryImp
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt module for providing repository dependencies.
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    
    /**
     * Binds the GameRepositoryImp implementation to the GameRepository interface.
     */
    @Binds
    @Singleton
    abstract fun bindGameRepository(
        gameRepositoryImp: GameRepositoryImp
    ): GameRepository
}
