package com.challanty.android.kp3.viewModel

import androidx.compose.ui.unit.IntSize
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.data.repository.GameRepository
import com.challanty.android.kp3.state.GameState
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.state.ProcessingType
import com.challanty.android.kp3.viewModel.helper.GameModelHelper
import com.challanty.android.kp3.viewModel.helper.GameStateHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class GameViewModel @Inject constructor(
    // TODO should we get rid of an unnecessary Repository?
    private val repository: GameRepository,
    private val processingStateManager: ProcessingStateManager,
    private val gameStateHelper: GameStateHelper,
    val gameModelHelper: GameModelHelper,
) : ViewModel() {

    private var readyToShowGame = false

    private var currentGamingAreaSize = IntSize.Zero

    private val selections = mutableSetOf<TileModel>()

    // Expose the game state for use in the UI
    fun getGameState(): StateFlow<GameState> = gameStateHelper.gameState

    fun onGameScreenSizeChange(newSize: IntSize) {
        // Build new game resources if needed
        println("GameViewModel: onGameScreenSizeChange called with size $newSize")

        // Ignore the zero size returned at startup and any redundant changes.
        if (newSize == IntSize.Zero || newSize == currentGamingAreaSize) {
            println("GameViewModel: onGameScreenSizeChange ignoring $newSize")
            return
        }

        println("GameViewModel: onGameScreenSizeChange setting currentGamingAreaSize to $newSize")
        currentGamingAreaSize = newSize

        handleGameChange(calcDimensions = true)
    }

    fun onNewGame() {
        handleGameChange(startNewGame = true)
    }

    fun onTileClick(tile: TileModel) {
        if (processingStateManager.isProcessing()) return

        if (selections.contains(tile)) {
            selections.remove(tile)
        } else {
            selections.add(tile)
        }

        tile.toggleSelection()

        if (selections.size == 2) {
            handleTileSwap(selections.first(), selections.last())
        }
    }

    fun onTileDoubleClick(tile: TileModel) {
        if (processingStateManager.isProcessing()) return

        if (selections.contains(tile)) {
            selections.remove(tile)
            tile.toggleSelection()
        }

        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            gameStateHelper.handleUITileRotation(tile)

            if (gameModelHelper.handlePuzzleTileRotation(tile)) {
                gameStateHelper.toggleSolvedBoard(true)
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }

    fun onRepositoryReady() {
        // This is the trigger that tells us that everything we need to
        // show the startup game is available.
        readyToShowGame = true
        println("GameViewModel: Repository ready")

        processingStateManager.endProcessing(ProcessingType.APP)

        handleGameChange(
            calcDimensions = true,
            startFirstGame = true
        )
    }

    fun onGameSettingsChanged(startNewGame: Boolean) {
        // TODO We really don't need to do anything here if a new game is not required?
        println("GameViewModel: gameSettingsChanged called with startNewGame: $startNewGame")

        if (startNewGame) {
            handleGameChange(
                calcDimensions = true,
                startNewGame = true
            )
        }
    }

    private fun handleGameChange(
        startNewGame: Boolean = false,
        calcDimensions: Boolean = false,
        startFirstGame: Boolean = false
    ) {
        // Note: There is always an existing game since we initialize the repository
        // with a default game.

        if (!readyToShowGame || currentGamingAreaSize == IntSize.Zero) {
            println("GameViewModel: handleGameChange called before repository is ready or game area size is known")
            return
        }

        // Block input and show progress indicator while handling game change
        // Note: The game setup is so fast that the progress indicator hardly gets started
        processingStateManager.startProcessing(ProcessingType.GAME)
        gameStateHelper.toggleProgress(true)

        viewModelScope.launch {

            if (calcDimensions) {
                gameModelHelper.calcGameDimensions(currentGamingAreaSize)
            }

            if (startNewGame) {
                gameStateHelper.showNewGame(gameModelHelper.buildNewGame())
            } else {
                if (startFirstGame) {
                    gameStateHelper.showStartupGame(gameModelHelper.buildExistingGame())
                } else {
                    gameStateHelper.showExistingGame(gameModelHelper.buildExistingGame())
                }
            }

            processingStateManager.endProcessing(ProcessingType.GAME)
        }

        return
    }

    private fun handleTileSwap(
        tile1: TileModel,
        tile2: TileModel
    ) {
        // Deselect the two selected tiles and swap their locations on the game board
        processingStateManager.startProcessing(ProcessingType.ANIMATION)

        viewModelScope.launch {

            tile1.toggleSelection()
            tile2.toggleSelection()

            selections.clear()

            gameStateHelper.handleUITileSwap(tile1, tile2)

            if (gameModelHelper.handlePuzzleTileSwap(tile1, tile2)) {
                gameStateHelper.toggleSolvedBoard(true)
            }

            processingStateManager.endProcessing(ProcessingType.ANIMATION)
        }
    }
}