package com.challanty.android.kp3.data.repository

import com.challanty.android.kp3.data.GameSettings
import com.google.protobuf.ByteString
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * Interface for the game repository.
 * This defines the contract for accessing and modifying game settings.
 */
interface GameRepository {
    /**
     * Initialize the repository with default values if not already initialized.
     * This should be called when the app starts.
     *
     * @param onInitialized Optional callback that will be invoked with the current settings when initialization is complete
     */
    fun initialize()

    /**
     * Check if the repository is ready to use.
     * @return A Flow that emits true if the repository has been initialized, false otherwise.
     */
    fun getInitialized(): Flow<Boolean>

    fun getGameSettings(): Flow<GameSettings>

    /**
     * Get the number of rows in the game board (1-6).
     * @return A Flow that emits the current number of rows.
     */
    fun getBoardRows(): Flow<Int>

    /**
     * Get the number of columns in the game board (1-6).
     * @return A Flow that emits the current number of columns.
     */
    fun getBoardCols(): Flow<Int>

    /**
     * Get the number of rows in each tile's picture elements (1-6).
     * @return A Flow that emits the current number of tile rows.
     */
    fun getTileRows(): Flow<Int>

    /**
     * Get the number of columns in each tile's picture elements (1-6).
     * @return A Flow that emits the current number of tile columns.
     */
    fun getTileCols(): Flow<Int>

    /**
     * Set the number of rows in the game board (1-6).
     * @param rows The new number of rows.
     */
    fun setBoardRows(rows: Int)

    /**
     * Set the number of columns in the game board (1-6).
     * @param columns The new number of columns.
     */
    fun setBoardCols(columns: Int)

    /**
     * Set the number of rows in each tile's picture elements (1-6).
     * @param rows The new number of tile rows.
     */
    fun setTileRows(rows: Int)

    /**
     * Set the number of columns in each tile's picture elements (1-6).
     * @param columns The new number of tile columns.
     */
    fun setTileCols(columns: Int)

    /**
     * Get whether tiles can be rotated.
     * @return A Flow that emits true if tiles can be rotated, false otherwise.
     */
    fun getTilesRotatable(): Flow<Boolean>

    /**
     * Set whether tiles can be rotated.
     * Note: Tiles can only be rotated if they have the same number of rows and columns.
     * @param rotatable Whether tiles can be rotated.
     */
    fun setTilesRotatable(rotatable: Boolean)

    /**
     * Get the current game board state as a ByteString.
     * @return A Flow that emits the current board state as a ByteString, or empty if not set.
     */
    fun getBoard(): Flow<ByteString>

    /**
     * Set the current game board state.
     * @param board The new board state as a ByteString.
     */
    fun setBoard(board: ByteString)

    /**
     * Get the current game solution as a ByteString.
     * @return A Flow that emits the current solution as a ByteString, or empty if not set.
     */
    fun getSolution(): Flow<ByteString>

    /**
     * Set the current game solution.
     * @param solution The new solution as a ByteString.
     */
    fun setSolution(solution: ByteString)

    /**
     * Get whether to animate game actions.
     * @return A Flow that emits true if animations are enabled, false otherwise.
     */
    fun getAnimate(): Flow<Boolean>

    /**
     * Set whether to animate game actions.
     * @param animate Whether to animate game actions.
     */
    fun setAnimate(animate: Boolean)

    /**
     * Get whether to animate tile rotations.
     * @return A Flow that emits true if rotation animations are enabled, false otherwise.
     */
    fun getAnimateRotation(): Flow<Boolean>

    /**
     * Set whether to animate tile rotations.
     * @param animateRotation Whether to animate tile rotations.
     */
    fun setAnimateRotation(animateRotation: Boolean)

    /**
     * Get whether to animate tile swaps.
     * @return A Flow that emits true if swap animations are enabled, false otherwise.
     */
    fun getAnimateSwap(): Flow<Boolean>

    /**
     * Set whether to animate tile swaps.
     * @param animateSwap Whether to animate tile swaps.
     */
    fun setAnimateSwap(animateSwap: Boolean)

    // TODO do we want to do this for all of the settings accessed by GameModelHelper and SettingsViewModel?
    val boardRows: StateFlow<Int>
}