package com.challanty.android.kp3.data.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.dataStore
import com.challanty.android.kp3.data.GameSettings
import com.challanty.android.kp3.state.ProcessingStateManager
import com.google.protobuf.ByteString
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton
import com.challanty.android.kp3.util.twoDintArray2ByteString
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn

// Create a DataStore instance using the gameSettingsDataStore extension property
private val Context.gameSettingsDataStore: DataStore<GameSettings> by dataStore(
    fileName = "game_settings.pb",
    serializer = GameSettingsSerializer()
)

/**
 * Implementation of GameRepository using Protocol Buffers and DataStore.
 */
@Singleton
class GameRepositoryImp @Inject constructor(
    @ApplicationContext private val context: Context
) : GameRepository {

    // TODO this is not used; should we get rid of it?
    @Inject
    lateinit var processingStateManager: ProcessingStateManager

    // Create a coroutine scope for repository operations
    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // Default values for board and tile dimensions
    companion object {
        const val DEFAULT_BOARD_ROWS = 4
        const val DEFAULT_BOARD_COLS = 4
        const val DEFAULT_TILE_ROWS = 2
        const val DEFAULT_TILE_COLS = 2
        const val DEFAULT_TILES_ROTATABLE = false
        const val DEFAULT_ANIMATE = true
        const val DEFAULT_ANIMATE_ROTATION = true
        const val DEFAULT_ANIMATE_SWAP = true

        // The default game solution.
        // These are picture unit IDs for 2x2 tiles in
        // 4 tile rows and 4 tile cols.
        val DEFAULT_SOLUTION = arrayOf(
            intArrayOf(20,  9,  7,  9,  7,  9,  7, 21),
            intArrayOf( 6, 17, 18, 17, 18, 17, 18, 10),
            intArrayOf( 8, 16, 19, 16, 19, 16, 19,  4),
            intArrayOf( 6, 17, 18, 17, 18, 17, 18, 10),
            intArrayOf( 8, 16, 19, 16, 19, 16, 19,  4),
            intArrayOf( 6, 17, 18, 17, 18, 17, 18, 10),
            intArrayOf( 8, 16, 19, 16, 19, 16, 19,  4),
            intArrayOf(23,  5, 11,  5, 11,  5, 11, 22)
        )

        // The default scrambled version of DEFAULT_SOLUTION
        val DEFAULT_BOARD = arrayOf(
            intArrayOf( 7, 21,  8, 16, 19,  4, 19, 16),
            intArrayOf(18, 10,  6, 17, 18, 10, 18, 17),
            intArrayOf(19, 16, 20,  9,  8, 16, 19, 16),
            intArrayOf(18, 17,  6, 17,  6, 17, 18, 17),
            intArrayOf( 7,  9, 19,  4, 19,  4, 19, 16),
            intArrayOf(18, 17, 18, 10, 11, 22, 11,  5),
            intArrayOf(19, 16,  8, 16,  7,  9, 19, 16),
            intArrayOf(18, 17, 23,  5, 18, 17, 11,  5)
        )

        // The default tile quarter turns
        val DEFAULT_TILE_QUARTER_TURNS = arrayOf(
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0),
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0),
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0),
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0),
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0),
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0),
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0),
            intArrayOf(0, 0, 0, 0, 0, 0, 0, 0)
        )

        const val MIN_DIMENSION = 1
        const val MAX_DIMENSION = 6
    }

    // Get the DataStore instance
    private val dataStore = context.gameSettingsDataStore

    /**
     * Initialize the repository with default values if not already initialized.
     */
    override fun initialize() {
        repositoryScope.launch {
            try {
                // Standard way to check if DataStore is empty
//                val isDataStoreEmpty = dataStore.data.first().toByteArray().isEmpty()

                // Check if already initialized
                val settings = dataStore.data
                    .catch { exception ->
                        // If an error occurs reading data, emit the default instance
                        emit(GameSettings.getDefaultInstance())
                    }
                    .firstOrNull()

                if (settings == null || !settings.initialized) {
                    // Initialize with default values
                    dataStore.updateData { currentSettings ->
                        currentSettings.toBuilder()
                            .setInitialized(true)
                            .setBoardRows(DEFAULT_BOARD_ROWS)
                            .setBoardCols(DEFAULT_BOARD_COLS)
                            .setTileRows(DEFAULT_TILE_ROWS)
                            .setTileCols(DEFAULT_TILE_COLS)
                            .setTilesRotatable(DEFAULT_TILES_ROTATABLE)
                            .setBoard(twoDintArray2ByteString(DEFAULT_BOARD))
                            .setSolution(twoDintArray2ByteString(DEFAULT_SOLUTION))
                            .setTileQuarterTurns(twoDintArray2ByteString(DEFAULT_TILE_QUARTER_TURNS))
                            .setAnimate(DEFAULT_ANIMATE)
                            .setAnimateRotation(DEFAULT_ANIMATE_ROTATION)
                            .setAnimateSwap(DEFAULT_ANIMATE_SWAP)
                            .build()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    // TODO we don't need this, but we do need a game version string
    /**
     * Check if the repository has been initialized.
     */
    override fun getInitialized(): Flow<Boolean> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map {
                it.initialized
            }
    }

    override fun getGameSettings(): Flow<GameSettings> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
    }

    override fun getBoardRows(): Flow<Int> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized or value is 0, return the default value
                if (!settings.initialized || settings.boardRows == 0) DEFAULT_BOARD_ROWS else settings.boardRows
            }
    }

    override fun getBoardCols(): Flow<Int> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized or value is 0, return the default value
                if (!settings.initialized || settings.boardCols == 0) DEFAULT_BOARD_COLS else settings.boardCols
            }
    }

    override fun getTileRows(): Flow<Int> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized or value is 0, return the default value
                if (!settings.initialized || settings.tileRows == 0) DEFAULT_TILE_ROWS else settings.tileRows
            }
    }

    override fun getTileCols(): Flow<Int> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized or value is 0, return the default value
                if (!settings.initialized || settings.tileCols == 0) DEFAULT_TILE_COLS else settings.tileCols
            }
    }

    override fun setBoardRows(rows: Int) {

        println("GameRepositoryImp: setBoardRows(): Setting board rows to $rows")
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setBoardRows(rows)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun setBoardCols(columns: Int) {

        println("GameRepositoryImp: setBoardCols(): Setting board columns to $columns")
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setBoardCols(columns)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun setTileRows(rows: Int) {

        println("GameRepositoryImp: setTileRows(): Setting tile rows to $rows")
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setTileRows(rows)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun setTileCols(columns: Int) {

        println("GameRepositoryImp: setTileCols(): Setting tile columns to $columns")
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setTileCols(columns)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun getTilesRotatable(): Flow<Boolean> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized, return the default value
                if (!settings.initialized) {
                    DEFAULT_TILES_ROTATABLE
                } else {
                    println("GameRepositoryImp: getTilesRotatable(): Returning value ${settings.tilesRotatable}")
                    settings.tilesRotatable
                } }
    }

    override fun setTilesRotatable(rotatable: Boolean) {
        println("GameRepositoryImp: setTilesRotatable(): Setting tiles rotatable to $rotatable")
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setTilesRotatable(rotatable)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun getBoard(): Flow<ByteString> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // Return the board or empty ByteString if not set
                if (settings.board != null && !settings.board.isEmpty) settings.board else ByteString.EMPTY
            }
    }

    override fun setBoard(board: ByteString) {
        // Launch a coroutine in the repository scope
        println("GameRepositoryImp: setBoard(): Setting board to $board")
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setBoard(board)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun getSolution(): Flow<ByteString> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // Return the solution or empty ByteString if not set
                if (settings.solution != null && !settings.solution.isEmpty) settings.solution else ByteString.EMPTY
            }
    }

    override fun setSolution(solution: ByteString) {
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setSolution(solution)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun getAnimate(): Flow<Boolean> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized, return the default value
                if (!settings.initialized) DEFAULT_ANIMATE else settings.animate
            }
    }

    override fun setAnimate(animate: Boolean) {
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setAnimate(animate)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun getAnimateRotation(): Flow<Boolean> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized, return the default value
                if (!settings.initialized) DEFAULT_ANIMATE_ROTATION else settings.animateRotation
            }
    }

    override fun setAnimateRotation(animateRotation: Boolean) {
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setAnimateRotation(animateRotation)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    override fun getAnimateSwap(): Flow<Boolean> {
        return dataStore.data
            .catch { exception ->
                // If an error occurs reading data, emit the default instance
                emit(GameSettings.getDefaultInstance())
            }
            .map { settings ->
                // If not initialized, return the default value
                if (!settings.initialized) DEFAULT_ANIMATE_SWAP else settings.animateSwap
            }
    }

    override fun setAnimateSwap(animateSwap: Boolean) {
        // Launch a coroutine in the repository scope
        repositoryScope.launch {
            try {
                dataStore.updateData { currentSettings ->
                    currentSettings.toBuilder()
                        .setAnimateSwap(animateSwap)
                        .build()
                }
            } catch (e: Exception) {
                // Log error or handle it as appropriate
                e.printStackTrace()
            }
        }
    }

    private val _boardRows: StateFlow<Int> = getBoardRows()
        .stateIn(
            scope = repositoryScope,
            started = SharingStarted.Eagerly, // Always active
            initialValue = 0
        )
    override val boardRows = _boardRows

}