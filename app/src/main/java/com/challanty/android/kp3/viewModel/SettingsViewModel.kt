package com.challanty.android.kp3.viewModel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.data.repository.GameRepository
import com.challanty.android.kp3.data.repository.GameRepositoryImp.Companion.MAX_DIMENSION
import com.challanty.android.kp3.data.repository.GameRepositoryImp.Companion.MIN_DIMENSION
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

/**
 * ViewModel for the Settings screen.
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val gameRepository: GameRepository
) : ViewModel() {

    // Current values from the repository
//    private val boardRows: StateFlow<Int> = gameRepository.getBoardRows()
//        .stateIn(
//            scope = viewModelScope,
//            started = SharingStarted.WhileSubscribed(5000),
//            initialValue = 0 // Default value until the actual value is loaded
//        )

    private val boardRows: StateFlow<Int> = gameRepository.boardRows

    private val boardCols: StateFlow<Int> = gameRepository.getBoardCols()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = 0 // Default value until the actual value is loaded
        )

    private val tileRows: StateFlow<Int> = gameRepository.getTileRows()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = 0 // Default value until the actual value is loaded
        )

    private val tileCols: StateFlow<Int> = gameRepository.getTileCols()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = 0 // Default value until the actual value is loaded
        )

    private val tilesRotatable: StateFlow<Boolean> = gameRepository.getTilesRotatable()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false // Default value until the actual value is loaded
        )

    private val animate: StateFlow<Boolean> = gameRepository.getAnimate()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false // Default value until the actual value is loaded
        )

    private val animateRotation: StateFlow<Boolean> = gameRepository.getAnimateRotation()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false // Default value until the actual value is loaded
        )

    private val animateSwap: StateFlow<Boolean> = gameRepository.getAnimateSwap()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false // Default value until the actual value is loaded
        )

    // User's in-progress changes (these persist across navigation)
    private val _pendingBoardRows = MutableStateFlow<Int?>(null)
    private val _pendingBoardCols = MutableStateFlow<Int?>(null)
    private val _pendingTileRows = MutableStateFlow<Int?>(null)
    private val _pendingTileCols = MutableStateFlow<Int?>(null)
    private val _pendingTilesRotatable = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimate = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimateRotation = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimateSwap = MutableStateFlow<Boolean?>(null)

    // Exposed values that combine current and pending values
    val settingsBoardRows: StateFlow<Int> = combine(
        boardRows,
        _pendingBoardRows
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 4
    )

    val settingBoardCols: StateFlow<Int> = combine(
        boardCols,
        _pendingBoardCols
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 4
    )

    val settingsTileRows: StateFlow<Int> = combine(
        tileRows,
        _pendingTileRows
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 2
    )

    val settingsTileColumns: StateFlow<Int> = combine(
        tileCols,
        _pendingTileCols
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 2
    )

    val settingsTilesRotatable: StateFlow<Boolean> = combine(
        tilesRotatable,
        _pendingTilesRotatable
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = true
    )

    val settingsAnimate: StateFlow<Boolean> = combine(
        animate,
        _pendingAnimate
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = true
    )

    val settingsAnimateRotation: StateFlow<Boolean> = combine(
        animateRotation,
        _pendingAnimateRotation
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = true
    )

    val settingsAnimateSwap: StateFlow<Boolean> = combine(
        animateSwap,
        _pendingAnimateSwap
    ) { current, pending ->
        pending ?: current
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = true
    )

    // Whether tiles can be rotated based on tile dimensions
    val settingsCanRotateTiles: StateFlow<Boolean> = combine(
        settingsTileRows,
        settingsTileColumns
    ) { rows, columns ->
        rows == columns // Tiles can only be rotated if they are square
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = true
    )

    // Valid values for tile rows based on matrix rows
    val settingsValidTileRowValues: StateFlow<List<Int>> = settingsBoardRows.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 4
    ).combine(settingsTileRows) { matrixRowsValue, tileRowsValue ->
        if (matrixRowsValue % 2 == 1) {
            // If matrix rows is odd, only allow even tile rows
            // TODO create these lists once
            listOf(2, 4, 6)
        } else {
            // If matrix rows is even, allow all tile row values
            listOf(1,2, 3, 4, 5, 6)
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = listOf(1,2, 3, 4, 5, 6)
    )

    // Valid values for tile columns based on matrix columns
    val settingsValidTileColumnValues: StateFlow<List<Int>> = settingBoardCols.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 4
    ).combine(settingsTileColumns) { matrixColumnsValue, tileColumnsValue ->
        if (matrixColumnsValue % 2 == 1) {
            // If matrix columns is odd, only allow even tile columns
            listOf(2, 4, 6)
        } else {
            // If matrix columns is even, allow all values
            listOf(1,2, 3, 4, 5, 6)
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = listOf(1,2, 3, 4, 5, 6)
    )

    // Check if matrix dimensions have pending changes
    private val hasBoardPendingChanges: StateFlow<Boolean> = combine(
        boardRows,
        boardCols,
        _pendingBoardRows,
        _pendingBoardCols
    ) { currentBoardR, currentBoardC, pendingBoardR, pendingBoardC ->
        (pendingBoardR != null && pendingBoardR != currentBoardR) ||
                (pendingBoardC != null && pendingBoardC != currentBoardC)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    // Check if tile dimensions have pending changes
    private val hasTilePendingChanges: StateFlow<Boolean> = combine(
        tileRows,
        tileCols,
        tilesRotatable,
        _pendingTileRows,
        _pendingTileCols,
        _pendingTilesRotatable
    ) { flows: Array<Any?> ->
        val currentTileR = flows[0] as Int
        val currentTileC = flows[1] as Int
        val currentRotatable = flows[2] as Boolean
        val pendingTileR = flows[3] as Int?
        val pendingTileC = flows[4] as Int?
        val pendingRotatable = flows[5] as Boolean?

        println("SettingsViewModel: hasTilePendingChanges: currentTileR=$currentTileR, currentTileC=$currentTileC, currentRotatable=$currentRotatable, pendingTileR=$pendingTileR, pendingTileC=$pendingTileC, pendingRotatable=$pendingRotatable")
        (pendingTileR != null && pendingTileR != currentTileR) ||
                (pendingTileC != null && pendingTileC != currentTileC) ||
                (pendingRotatable != null && pendingRotatable != currentRotatable)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    // Check if animation settings have pending changes
    private val hasAnimationPendingChanges: StateFlow<Boolean> = combine(
        animate,
        animateRotation,
        animateSwap,
        _pendingAnimate,
        _pendingAnimateRotation,
        _pendingAnimateSwap
    ) { flows: Array<Any?> ->
        val currentAnimate = flows[0] as Boolean
        val currentAnimateRotation = flows[1] as Boolean
        val currentAnimateSwap = flows[2] as Boolean
        val pendingAnimate = flows[3] as Boolean?
        val pendingAnimateRotation = flows[4] as Boolean?
        val pendingAnimateSwap = flows[5] as Boolean?

        (pendingAnimate != null && pendingAnimate != currentAnimate) ||
                (pendingAnimateRotation != null && pendingAnimateRotation != currentAnimateRotation) ||
                (pendingAnimateSwap != null && pendingAnimateSwap != currentAnimateSwap)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    // Flag to indicate if there are any pending changes
    val hasPendingChanges: StateFlow<Boolean> = combine(
        hasBoardPendingChanges,
        hasTilePendingChanges,
        hasAnimationPendingChanges
    ) { hasMatrixChanges, hasTileChanges, hasAnimationChanges ->
        println("SettingsViewModel: hasPendingChanges: hasMatrixChanges=$hasMatrixChanges, hasTileChanges=$hasTileChanges, hasAnimationChanges=$hasAnimationChanges")
        hasMatrixChanges || hasTileChanges || hasAnimationChanges
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly, // Always active
        initialValue = false
    )

    /**
     * Update the pending number of rows in the game board.
     * This doesn't update the repository yet, just stores the pending change.
     * Also adjusts tile rows if needed to maintain even product constraint.
     */
    fun setPendingMatrixRows(rows: Int) {
        val newRows = rows.coerceIn(MIN_DIMENSION, MAX_DIMENSION)
        _pendingBoardRows.value = newRows

        // If matrix rows is now odd, ensure tile rows is even
        if (newRows % 2 == 1) {
            val currentTileRowsValue = _pendingTileRows.value ?: tileRows.value
            if (currentTileRowsValue % 2 == 1) {
                // Current tile rows is odd, need to adjust
                if (currentTileRowsValue > 1) {
                    // Decrease by 1 to make it even
                    _pendingTileRows.value = currentTileRowsValue - 1
                } else {
                    // If it's 1, set to 2
                    _pendingTileRows.value = 2
                }
            }
        }
    }

    /**
     * Update the pending number of columns in the game board.
     * This doesn't update the repository yet, just stores the pending change.
     * Also adjusts tile columns if needed to maintain even product constraint.
     */
    fun setPendingMatrixColumns(columns: Int) {
        val newColumns = columns.coerceIn(MIN_DIMENSION, MAX_DIMENSION)
        _pendingBoardCols.value = newColumns

        // If matrix columns is now odd, ensure tile columns is even
        if (newColumns % 2 == 1) {
            val currentTileColsValue = _pendingTileCols.value ?: tileCols.value
            if (currentTileColsValue % 2 == 1) {
                // Current tile columns is odd, need to adjust
                if (currentTileColsValue > 1) {
                    // Decrease by 1 to make it even
                    _pendingTileCols.value = currentTileColsValue - 1
                } else {
                    // If it's 1, set to 2
                    _pendingTileCols.value = 2
                }
            }
        }
    }

    /**
     * Update the pending number of rows in each tile's picture elements.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingTileRows(rows: Int) {
        val boardRowsValue = _pendingBoardRows.value ?: boardRows.value

        // If board rows is odd, ensure tile rows is even
        if (boardRowsValue % 2 == 1 && rows % 2 == 1) {
            // Can't set an odd value for tile rows when board rows is odd
            return
        }

        _pendingTileRows.value = rows.coerceIn(MIN_DIMENSION, MAX_DIMENSION)

        // Check if we need to update the rotatable setting
        updateRotatableSetting()
    }

    /**
     * Update the pending number of columns in each tile's picture elements.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingTileColumns(columns: Int) {
        val boardColsValue = _pendingBoardCols.value ?: boardCols.value

        // If board columns is odd, ensure tile columns is even
        if (boardColsValue % 2 == 1 && columns % 2 == 1) {
            // Can't set an odd value for tile columns when board columns is odd
            return
        }

        _pendingTileCols.value = columns.coerceIn(MIN_DIMENSION, MAX_DIMENSION)

        // Check if we need to update the rotatable setting
        updateRotatableSetting()
    }

    /**
     * Update the pending setting for whether tiles can be rotated.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingTilesRotatable(rotatable: Boolean) {
        // Only allow setting to true if tiles are square
        val tileRowsValue = _pendingTileRows.value ?: tileRows.value
        val tileColsValue = _pendingTileCols.value ?: tileCols.value

        if (rotatable && tileRowsValue != tileColsValue) {
            // Can't enable rotation for non-square tiles
            return
        }

        _pendingTilesRotatable.value = rotatable
    }

    /**
     * Update the rotatable setting based on tile dimensions.
     * If tiles are not square, rotation must be disabled.
     */
    private fun updateRotatableSetting() {
        val tileRowsValue = _pendingTileRows.value ?: tileRows.value
        val tileColsValue = _pendingTileCols.value ?: tileCols.value

        // If tiles are not square, force rotatable to false
        if (tileRowsValue != tileColsValue) {
            _pendingTilesRotatable.value = false
        }
    }

    /**
     * Update the pending setting for whether to animate game actions.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingAnimate(animate: Boolean) {
        _pendingAnimate.value = animate
    }

    /**
     * Update the pending setting for whether to animate tile rotations.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingAnimateRotation(animateRotation: Boolean) {
        _pendingAnimateRotation.value = animateRotation
    }

    /**
     * Update the pending setting for whether to animate tile swaps.
     * This doesn't update the repository yet, just stores the pending change.
     */
    fun setPendingAnimateSwap(animateSwap: Boolean) {
        _pendingAnimateSwap.value = animateSwap
    }

    /**
     * Apply all pending changes to the repository.
     * This is called automatically when the user navigates away from the Settings screen
     * or when the app is shut down.
     *
     * @return True if a new game is required, false otherwise
     */
    fun applyChanges(): Boolean {
        println("SettingsViewModel: applyChanges()")
        val pendingBoardRows = _pendingBoardRows.value
        val pendingBoardCols = _pendingBoardCols.value
        val pendingTileRows = _pendingTileRows.value
        val pendingTileCols = _pendingTileCols.value
        val pendingTilesRotatable = _pendingTilesRotatable.value
        val pendingAnimate = _pendingAnimate.value
        val pendingAnimateRotation = _pendingAnimateRotation.value
        val pendingAnimateSwap = _pendingAnimateSwap.value

        var newGameRequired = false

        if (pendingBoardRows != null && pendingBoardRows != boardRows.value) {
            println("SettingsViewModel: applyChanges(): Setting board rows to $pendingBoardRows")
            gameRepository.setBoardRows(pendingBoardRows)
            newGameRequired = true
        }

        if (pendingBoardCols != null && pendingBoardCols != boardCols.value) {
            println("SettingsViewModel: applyChanges(): Setting board columns to $pendingBoardCols")
            gameRepository.setBoardCols(pendingBoardCols)
            newGameRequired = true
        }

        if (pendingTileRows != null && pendingTileRows != tileRows.value) {
            println("SettingsViewModel: applyChanges(): Setting tile rows to $pendingTileRows")
            gameRepository.setTileRows(pendingTileRows)
            newGameRequired = true
        }

        if (pendingTileCols != null && pendingTileCols != tileCols.value) {
            println("SettingsViewModel: applyChanges(): Setting tile columns to $pendingTileCols")
            gameRepository.setTileCols(pendingTileCols)
            newGameRequired = true
        }

        println("SettingsViewModel: pendingTilesRotatable: $pendingTilesRotatable; currentTilesRotatable: ${tilesRotatable.value}")
        if (pendingTilesRotatable != null && pendingTilesRotatable != tilesRotatable.value) {
            gameRepository.setTilesRotatable(pendingTilesRotatable)
        }

        if (pendingAnimate != null && pendingAnimate != animate.value) {
            gameRepository.setAnimate(pendingAnimate)
        }

        if (pendingAnimateRotation != null && pendingAnimateRotation != animateRotation.value) {
            gameRepository.setAnimateRotation(pendingAnimateRotation)
        }

        if (pendingAnimateSwap != null && pendingAnimateSwap != animateSwap.value) {
            gameRepository.setAnimateSwap(pendingAnimateSwap)
        }

        // Clear pending changes after applying them
        _pendingBoardRows.value = null
        _pendingBoardCols.value = null
        _pendingTileRows.value = null
        _pendingTileCols.value = null
        _pendingTilesRotatable.value = null
        _pendingAnimate.value = null
        _pendingAnimateRotation.value = null
        _pendingAnimateSwap.value = null

        return newGameRequired
    }

    /**
     * Called when the ViewModel is being cleared (e.g., when the user navigates away
     * or when the app is shut down).
     */
    override fun onCleared() {
        super.onCleared()
        // Apply any pending changes before the ViewModel is cleared
        if (hasPendingChanges.value) {
            applyChanges()
        }
    }
}
